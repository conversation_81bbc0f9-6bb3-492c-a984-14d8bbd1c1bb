package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// ConstConfig ConstConfig

type constConfig struct {
	data map[int32]*pb.ListConstConfig_ConstConfig
	ids  []int32
}

var constConfigFunc = func() *pb.ListConstConfig { return &pb.ListConstConfig{} }

var ConstConfig *constConfig

func newConstConfig() *constConfig {
	result := &constConfig{
		data: make(map[int32]*pb.ListConstConfig_ConstConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "ConstConfig.bytes"), constConfigFunc)
	if err != nil {
		logger.Errorf("load ConstConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *constConfig) Item(id int32) *pb.ListConstConfig_ConstConfig {
	return c.data[id]
}

func (c *constConfig) Items() []*pb.ListConstConfig_ConstConfig {
	items := make([]*pb.ListConstConfig_ConstConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *constConfig) GetIds() []int32 {
	return c.ids
}
