package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y英雄表.xlsx HeroLevelConfig

type heroLevelConfig struct {
	data map[int32]*pb.ListHeroLevelConfig_HeroLevelConfig
	ids  []int32
}

var heroLevelConfigFunc = func() *pb.ListHeroLevelConfig { return &pb.ListHeroLevelConfig{} }

var HeroLevelConfig *heroLevelConfig

func newHeroLevelConfig() *heroLevelConfig {
	result := &heroLevelConfig{
		data: make(map[int32]*pb.ListHeroLevelConfig_HeroLevelConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "HeroLevelConfig.bytes"), heroLevelConfigFunc)
	if err != nil {
		logger.Errorf("load HeroLevelConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *heroLevelConfig) Item(id int32) *pb.ListHeroLevelConfig_HeroLevelConfig {
	return c.data[id]
}

func (c *heroLevelConfig) Items() []*pb.ListHeroLevelConfig_HeroLevelConfig {
	items := make([]*pb.ListHeroLevelConfig_HeroLevelConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *heroLevelConfig) GetIds() []int32 {
	return c.ids
}
