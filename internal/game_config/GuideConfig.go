package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y引导表.xlsx GuideConfig

type guideConfig struct {
	data map[int32]*pb.ListGuideConfig_GuideConfig
	ids  []int32
}

var guideConfigFunc = func() *pb.ListGuideConfig { return &pb.ListGuideConfig{} }

var GuideConfig *guideConfig

func newGuideConfig() *guideConfig {
	result := &guideConfig{
		data: make(map[int32]*pb.ListGuideConfig_GuideConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "GuideConfig.bytes"), guideConfigFunc)
	if err != nil {
		logger.Errorf("load GuideConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *guideConfig) Item(id int32) *pb.ListGuideConfig_GuideConfig {
	return c.data[id]
}

func (c *guideConfig) Items() []*pb.ListGuideConfig_GuideConfig {
	items := make([]*pb.ListGuideConfig_GuideConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *guideConfig) GetIds() []int32 {
	return c.ids
}
