package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// AI行为配置表.xlsx AIActionConfig

type aIActionConfig struct {
	data map[int32]*pb.ListAIActionConfig_AIActionConfig
	ids  []int32
}

var aIActionConfigFunc = func() *pb.ListAIActionConfig { return &pb.ListAIActionConfig{} }

var AIActionConfig *aIActionConfig

func newAIActionConfig() *aIActionConfig {
	result := &aIActionConfig{
		data: make(map[int32]*pb.ListAIActionConfig_AIActionConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "AIActionConfig.bytes"), aIActionConfigFunc)
	if err != nil {
		logger.Errorf("load AIActionConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *aIActionConfig) Item(id int32) *pb.ListAIActionConfig_AIActionConfig {
	return c.data[id]
}

func (c *aIActionConfig) Items() []*pb.ListAIActionConfig_AIActionConfig {
	items := make([]*pb.ListAIActionConfig_AIActionConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *aIActionConfig) GetIds() []int32 {
	return c.ids
}
