package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// C充值表.xlsx RechargeConfig

type rechargeConfig struct {
	data map[int32]*pb.ListRechargeConfig_RechargeConfig
	ids  []int32
}

var rechargeConfigFunc = func() *pb.ListRechargeConfig { return &pb.ListRechargeConfig{} }

var RechargeConfig *rechargeConfig

func newRechargeConfig() *rechargeConfig {
	result := &rechargeConfig{
		data: make(map[int32]*pb.ListRechargeConfig_RechargeConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "RechargeConfig.bytes"), rechargeConfigFunc)
	if err != nil {
		logger.Errorf("load RechargeConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *rechargeConfig) Item(id int32) *pb.ListRechargeConfig_RechargeConfig {
	return c.data[id]
}

func (c *rechargeConfig) Items() []*pb.ListRechargeConfig_RechargeConfig {
	items := make([]*pb.ListRechargeConfig_RechargeConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *rechargeConfig) GetIds() []int32 {
	return c.ids
}
