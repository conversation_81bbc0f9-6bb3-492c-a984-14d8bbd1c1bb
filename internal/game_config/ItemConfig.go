package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// W物品表.xlsx ItemConfig

type itemConfig struct {
	data map[int32]*pb.ListItemConfig_ItemConfig
	ids  []int32
}

var itemConfigFunc = func() *pb.ListItemConfig { return &pb.ListItemConfig{} }

var ItemConfig *itemConfig

func newItemConfig() *itemConfig {
	result := &itemConfig{
		data: make(map[int32]*pb.ListItemConfig_ItemConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "ItemConfig.bytes"), itemConfigFunc)
	if err != nil {
		logger.Errorf("load ItemConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *itemConfig) Item(id int32) *pb.ListItemConfig_ItemConfig {
	return c.data[id]
}

func (c *itemConfig) Items() []*pb.ListItemConfig_ItemConfig {
	items := make([]*pb.ListItemConfig_ItemConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *itemConfig) GetIds() []int32 {
	return c.ids
}
