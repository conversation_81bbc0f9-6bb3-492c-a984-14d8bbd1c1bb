package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// X系统开启表.xlsx SystemOpenConfig

type systemOpenConfig struct {
	data map[int32]*pb.ListSystemOpenConfig_SystemOpenConfig
	ids  []int32
}

var systemOpenConfigFunc = func() *pb.ListSystemOpenConfig { return &pb.ListSystemOpenConfig{} }

var SystemOpenConfig *systemOpenConfig

func newSystemOpenConfig() *systemOpenConfig {
	result := &systemOpenConfig{
		data: make(map[int32]*pb.ListSystemOpenConfig_SystemOpenConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "SystemOpenConfig.bytes"), systemOpenConfigFunc)
	if err != nil {
		logger.Errorf("load SystemOpenConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *systemOpenConfig) Item(id int32) *pb.ListSystemOpenConfig_SystemOpenConfig {
	return c.data[id]
}

func (c *systemOpenConfig) Items() []*pb.ListSystemOpenConfig_SystemOpenConfig {
	items := make([]*pb.ListSystemOpenConfig_SystemOpenConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *systemOpenConfig) GetIds() []int32 {
	return c.ids
}
