package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// J剧情故事.xlsx StoryPlotConfig

type storyPlotConfig struct {
	data map[int32]*pb.ListStoryPlotConfig_StoryPlotConfig
	ids  []int32
}

var storyPlotConfigFunc = func() *pb.ListStoryPlotConfig { return &pb.ListStoryPlotConfig{} }

var StoryPlotConfig *storyPlotConfig

func newStoryPlotConfig() *storyPlotConfig {
	result := &storyPlotConfig{
		data: make(map[int32]*pb.ListStoryPlotConfig_StoryPlotConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "StoryPlotConfig.bytes"), storyPlotConfigFunc)
	if err != nil {
		logger.Errorf("load StoryPlotConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *storyPlotConfig) Item(id int32) *pb.ListStoryPlotConfig_StoryPlotConfig {
	return c.data[id]
}

func (c *storyPlotConfig) Items() []*pb.ListStoryPlotConfig_StoryPlotConfig {
	items := make([]*pb.ListStoryPlotConfig_StoryPlotConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *storyPlotConfig) GetIds() []int32 {
	return c.ids
}
