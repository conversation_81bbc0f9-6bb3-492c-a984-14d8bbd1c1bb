package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y英雄表.xlsx HeroConfig

type heroConfig struct {
	data map[int32]*pb.ListHeroConfig_HeroConfig
	ids  []int32
}

var heroConfigFunc = func() *pb.ListHeroConfig { return &pb.ListHeroConfig{} }

var HeroConfig *heroConfig

func newHeroConfig() *heroConfig {
	result := &heroConfig{
		data: make(map[int32]*pb.ListHeroConfig_HeroConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "HeroConfig.bytes"), heroConfigFunc)
	if err != nil {
		logger.Errorf("load HeroConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *heroConfig) Item(id int32) *pb.ListHeroConfig_HeroConfig {
	return c.data[id]
}

func (c *heroConfig) Items() []*pb.ListHeroConfig_HeroConfig {
	items := make([]*pb.ListHeroConfig_HeroConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *heroConfig) GetIds() []int32 {
	return c.ids
}
