package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// B标签表.xlsx TagConfig

type tagConfig struct {
	data map[int32]*pb.ListTagConfig_TagConfig
	ids  []int32
}

var tagConfigFunc = func() *pb.ListTagConfig { return &pb.ListTagConfig{} }

var TagConfig *tagConfig

func newTagConfig() *tagConfig {
	result := &tagConfig{
		data: make(map[int32]*pb.ListTagConfig_TagConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "TagConfig.bytes"), tagConfigFunc)
	if err != nil {
		logger.Errorf("load TagConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *tagConfig) Item(id int32) *pb.ListTagConfig_TagConfig {
	return c.data[id]
}

func (c *tagConfig) Items() []*pb.ListTagConfig_TagConfig {
	items := make([]*pb.ListTagConfig_TagConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *tagConfig) GetIds() []int32 {
	return c.ids
}
