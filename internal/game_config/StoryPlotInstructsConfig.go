package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// J剧情故事.xlsx StoryPlotInstructsConfig

type storyPlotInstructsConfig struct {
	data map[int32]*pb.ListStoryPlotInstructsConfig_StoryPlotInstructsConfig
	ids  []int32
}

var storyPlotInstructsConfigFunc = func() *pb.ListStoryPlotInstructsConfig { return &pb.ListStoryPlotInstructsConfig{} }

var StoryPlotInstructsConfig *storyPlotInstructsConfig

func newStoryPlotInstructsConfig() *storyPlotInstructsConfig {
	result := &storyPlotInstructsConfig{
		data: make(map[int32]*pb.ListStoryPlotInstructsConfig_StoryPlotInstructsConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "StoryPlotInstructsConfig.bytes"), storyPlotInstructsConfigFunc)
	if err != nil {
		logger.Errorf("load StoryPlotInstructsConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *storyPlotInstructsConfig) Item(id int32) *pb.ListStoryPlotInstructsConfig_StoryPlotInstructsConfig {
	return c.data[id]
}

func (c *storyPlotInstructsConfig) Items() []*pb.ListStoryPlotInstructsConfig_StoryPlotInstructsConfig {
	items := make([]*pb.ListStoryPlotInstructsConfig_StoryPlotInstructsConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *storyPlotInstructsConfig) GetIds() []int32 {
	return c.ids
}
