package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// P排行榜.xlsx RankConfig

type rankConfig struct {
	data map[int32]*pb.ListRankConfig_RankConfig
	ids  []int32
}

var rankConfigFunc = func() *pb.ListRankConfig { return &pb.ListRankConfig{} }

var RankConfig *rankConfig

func newRankConfig() *rankConfig {
	result := &rankConfig{
		data: make(map[int32]*pb.ListRankConfig_RankConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "RankConfig.bytes"), rankConfigFunc)
	if err != nil {
		logger.Errorf("load RankConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *rankConfig) Item(id int32) *pb.ListRankConfig_RankConfig {
	return c.data[id]
}

func (c *rankConfig) Items() []*pb.ListRankConfig_RankConfig {
	items := make([]*pb.ListRankConfig_RankConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *rankConfig) GetIds() []int32 {
	return c.ids
}
