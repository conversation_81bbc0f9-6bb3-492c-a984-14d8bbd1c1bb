package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// AI行为配置表.xlsx AIConditionConfig

type aIConditionConfig struct {
	data map[int32]*pb.ListAIConditionConfig_AIConditionConfig
	ids  []int32
}

var aIConditionConfigFunc = func() *pb.ListAIConditionConfig { return &pb.ListAIConditionConfig{} }

var AIConditionConfig *aIConditionConfig

func newAIConditionConfig() *aIConditionConfig {
	result := &aIConditionConfig{
		data: make(map[int32]*pb.ListAIConditionConfig_AIConditionConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "AIConditionConfig.bytes"), aIConditionConfigFunc)
	if err != nil {
		logger.Errorf("load AIConditionConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *aIConditionConfig) Item(id int32) *pb.ListAIConditionConfig_AIConditionConfig {
	return c.data[id]
}

func (c *aIConditionConfig) Items() []*pb.ListAIConditionConfig_AIConditionConfig {
	items := make([]*pb.ListAIConditionConfig_AIConditionConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *aIConditionConfig) GetIds() []int32 {
	return c.ids
}
