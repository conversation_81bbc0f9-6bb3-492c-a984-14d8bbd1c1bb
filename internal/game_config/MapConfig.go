package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// C场景配置.xlsx MapConfig

type mapConfig struct {
	data map[int32]*pb.ListMapConfig_MapConfig
	ids  []int32
}

var mapConfigFunc = func() *pb.ListMapConfig { return &pb.ListMapConfig{} }

var MapConfig *mapConfig

func newMapConfig() *mapConfig {
	result := &mapConfig{
		data: make(map[int32]*pb.ListMapConfig_MapConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "MapConfig.bytes"), mapConfigFunc)
	if err != nil {
		logger.Errorf("load MapConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *mapConfig) Item(id int32) *pb.ListMapConfig_MapConfig {
	return c.data[id]
}

func (c *mapConfig) Items() []*pb.ListMapConfig_MapConfig {
	items := make([]*pb.ListMapConfig_MapConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *mapConfig) GetIds() []int32 {
	return c.ids
}
