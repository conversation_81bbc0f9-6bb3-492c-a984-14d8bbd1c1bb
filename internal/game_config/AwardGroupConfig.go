package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// J奖励表.xlsx AwardGroupConfig

type awardGroupConfig struct {
	data map[int32]*pb.ListAwardGroupConfig_AwardGroupConfig
	ids  []int32
}

var awardGroupConfigFunc = func() *pb.ListAwardGroupConfig { return &pb.ListAwardGroupConfig{} }

var AwardGroupConfig *awardGroupConfig

func newAwardGroupConfig() *awardGroupConfig {
	result := &awardGroupConfig{
		data: make(map[int32]*pb.ListAwardGroupConfig_AwardGroupConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "AwardGroupConfig.bytes"), awardGroupConfigFunc)
	if err != nil {
		logger.Errorf("load AwardGroupConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *awardGroupConfig) Item(id int32) *pb.ListAwardGroupConfig_AwardGroupConfig {
	return c.data[id]
}

func (c *awardGroupConfig) Items() []*pb.ListAwardGroupConfig_AwardGroupConfig {
	items := make([]*pb.ListAwardGroupConfig_AwardGroupConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *awardGroupConfig) GetIds() []int32 {
	return c.ids
}
