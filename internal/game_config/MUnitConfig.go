package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// MUnitConfig MUnitConfig

type mUnitConfig struct {
	data map[int32]*pb.ListMUnitConfig_MUnitConfig
	ids  []int32
}

var mUnitConfigFunc = func() *pb.ListMUnitConfig { return &pb.ListMUnitConfig{} }

var MUnitConfig *mUnitConfig

func newMUnitConfig() *mUnitConfig {
	result := &mUnitConfig{
		data: make(map[int32]*pb.ListMUnitConfig_MUnitConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "MUnitConfig.bytes"), mUnitConfigFunc)
	if err != nil {
		logger.Errorf("load MUnitConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *mUnitConfig) Item(id int32) *pb.ListMUnitConfig_MUnitConfig {
	return c.data[id]
}

func (c *mUnitConfig) Items() []*pb.ListMUnitConfig_MUnitConfig {
	items := make([]*pb.ListMUnitConfig_MUnitConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *mUnitConfig) GetIds() []int32 {
	return c.ids
}
