package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// R任务.xlsx MainTaskConfig

type mainTaskConfig struct {
	data map[int32]*pb.ListMainTaskConfig_MainTaskConfig
	ids  []int32
}

var mainTaskConfigFunc = func() *pb.ListMainTaskConfig { return &pb.ListMainTaskConfig{} }

var MainTaskConfig *mainTaskConfig

func newMainTaskConfig() *mainTaskConfig {
	result := &mainTaskConfig{
		data: make(map[int32]*pb.ListMainTaskConfig_MainTaskConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "MainTaskConfig.bytes"), mainTaskConfigFunc)
	if err != nil {
		logger.Errorf("load MainTaskConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *mainTaskConfig) Item(id int32) *pb.ListMainTaskConfig_MainTaskConfig {
	return c.data[id]
}

func (c *mainTaskConfig) Items() []*pb.ListMainTaskConfig_MainTaskConfig {
	items := make([]*pb.ListMainTaskConfig_MainTaskConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *mainTaskConfig) GetIds() []int32 {
	return c.ids
}
