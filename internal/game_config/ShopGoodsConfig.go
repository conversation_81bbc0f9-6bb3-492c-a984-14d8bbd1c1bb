package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// S商城表.xlsx ShopGoodsConfig

type shopGoodsConfig struct {
	data map[int32]*pb.ListShopGoodsConfig_ShopGoodsConfig
	ids  []int32
}

var shopGoodsConfigFunc = func() *pb.ListShopGoodsConfig { return &pb.ListShopGoodsConfig{} }

var ShopGoodsConfig *shopGoodsConfig

func newShopGoodsConfig() *shopGoodsConfig {
	result := &shopGoodsConfig{
		data: make(map[int32]*pb.ListShopGoodsConfig_ShopGoodsConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "ShopGoodsConfig.bytes"), shopGoodsConfigFunc)
	if err != nil {
		logger.Errorf("load ShopGoodsConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *shopGoodsConfig) Item(id int32) *pb.ListShopGoodsConfig_ShopGoodsConfig {
	return c.data[id]
}

func (c *shopGoodsConfig) Items() []*pb.ListShopGoodsConfig_ShopGoodsConfig {
	items := make([]*pb.ListShopGoodsConfig_ShopGoodsConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *shopGoodsConfig) GetIds() []int32 {
	return c.ids
}
