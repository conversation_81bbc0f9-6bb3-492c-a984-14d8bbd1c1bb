package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// T条件触发表.xlsx ConditionTriggerConfig

type conditionTriggerConfig struct {
	data map[int32]*pb.ListConditionTriggerConfig_ConditionTriggerConfig
	ids  []int32
}

var conditionTriggerConfigFunc = func() *pb.ListConditionTriggerConfig { return &pb.ListConditionTriggerConfig{} }

var ConditionTriggerConfig *conditionTriggerConfig

func newConditionTriggerConfig() *conditionTriggerConfig {
	result := &conditionTriggerConfig{
		data: make(map[int32]*pb.ListConditionTriggerConfig_ConditionTriggerConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "ConditionTriggerConfig.bytes"), conditionTriggerConfigFunc)
	if err != nil {
		logger.Errorf("load ConditionTriggerConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *conditionTriggerConfig) Item(id int32) *pb.ListConditionTriggerConfig_ConditionTriggerConfig {
	return c.data[id]
}

func (c *conditionTriggerConfig) Items() []*pb.ListConditionTriggerConfig_ConditionTriggerConfig {
	items := make([]*pb.ListConditionTriggerConfig_ConditionTriggerConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *conditionTriggerConfig) GetIds() []int32 {
	return c.ids
}
