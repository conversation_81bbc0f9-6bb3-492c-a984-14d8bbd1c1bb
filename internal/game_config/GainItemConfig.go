package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// H获得物品表现配置.xlsx GainItemConfig

type gainItemConfig struct {
	data map[int32]*pb.ListGainItemConfig_GainItemConfig
	ids  []int32
}

var gainItemConfigFunc = func() *pb.ListGainItemConfig { return &pb.ListGainItemConfig{} }

var GainItemConfig *gainItemConfig

func newGainItemConfig() *gainItemConfig {
	result := &gainItemConfig{
		data: make(map[int32]*pb.ListGainItemConfig_GainItemConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "GainItemConfig.bytes"), gainItemConfigFunc)
	if err != nil {
		logger.Errorf("load GainItemConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *gainItemConfig) Item(id int32) *pb.ListGainItemConfig_GainItemConfig {
	return c.data[id]
}

func (c *gainItemConfig) Items() []*pb.ListGainItemConfig_GainItemConfig {
	items := make([]*pb.ListGainItemConfig_GainItemConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *gainItemConfig) GetIds() []int32 {
	return c.ids
}
