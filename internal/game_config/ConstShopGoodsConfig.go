package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// S商城表.xlsx ConstShopGoodsConfig

type constShopGoodsConfig struct {
	data map[int32]*pb.ListConstShopGoodsConfig_ConstShopGoodsConfig
	ids  []int32
}

var constShopGoodsConfigFunc = func() *pb.ListConstShopGoodsConfig { return &pb.ListConstShopGoodsConfig{} }

var ConstShopGoodsConfig *constShopGoodsConfig

func newConstShopGoodsConfig() *constShopGoodsConfig {
	result := &constShopGoodsConfig{
		data: make(map[int32]*pb.ListConstShopGoodsConfig_ConstShopGoodsConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "ConstShopGoodsConfig.bytes"), constShopGoodsConfigFunc)
	if err != nil {
		logger.Errorf("load ConstShopGoodsConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *constShopGoodsConfig) Item(id int32) *pb.ListConstShopGoodsConfig_ConstShopGoodsConfig {
	return c.data[id]
}

func (c *constShopGoodsConfig) Items() []*pb.ListConstShopGoodsConfig_ConstShopGoodsConfig {
	items := make([]*pb.ListConstShopGoodsConfig_ConstShopGoodsConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *constShopGoodsConfig) GetIds() []int32 {
	return c.ids
}
