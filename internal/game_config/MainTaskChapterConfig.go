package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// R任务.xlsx MainTaskChapterConfig

type mainTaskChapterConfig struct {
	data map[int32]*pb.ListMainTaskChapterConfig_MainTaskChapterConfig
	ids  []int32
}

var mainTaskChapterConfigFunc = func() *pb.ListMainTaskChapterConfig { return &pb.ListMainTaskChapterConfig{} }

var MainTaskChapterConfig *mainTaskChapterConfig

func newMainTaskChapterConfig() *mainTaskChapterConfig {
	result := &mainTaskChapterConfig{
		data: make(map[int32]*pb.ListMainTaskChapterConfig_MainTaskChapterConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "MainTaskChapterConfig.bytes"), mainTaskChapterConfigFunc)
	if err != nil {
		logger.Errorf("load MainTaskChapterConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *mainTaskChapterConfig) Item(id int32) *pb.ListMainTaskChapterConfig_MainTaskChapterConfig {
	return c.data[id]
}

func (c *mainTaskChapterConfig) Items() []*pb.ListMainTaskChapterConfig_MainTaskChapterConfig {
	items := make([]*pb.ListMainTaskChapterConfig_MainTaskChapterConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *mainTaskChapterConfig) GetIds() []int32 {
	return c.ids
}
