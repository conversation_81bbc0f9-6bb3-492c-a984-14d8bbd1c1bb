package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Z主角表.xlsx LevelConfig

type levelConfig struct {
	data map[int32]*pb.ListLevelConfig_LevelConfig
	ids  []int32
}

var levelConfigFunc = func() *pb.ListLevelConfig { return &pb.ListLevelConfig{} }

var LevelConfig *levelConfig

func newLevelConfig() *levelConfig {
	result := &levelConfig{
		data: make(map[int32]*pb.ListLevelConfig_LevelConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "LevelConfig.bytes"), levelConfigFunc)
	if err != nil {
		logger.Errorf("load LevelConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *levelConfig) Item(id int32) *pb.ListLevelConfig_LevelConfig {
	return c.data[id]
}

func (c *levelConfig) Items() []*pb.ListLevelConfig_LevelConfig {
	items := make([]*pb.ListLevelConfig_LevelConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *levelConfig) GetIds() []int32 {
	return c.ids
}
