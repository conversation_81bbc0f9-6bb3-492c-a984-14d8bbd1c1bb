package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// X形象表.xlsx AvatarConfig

type avatarConfig struct {
	data map[int32]*pb.ListAvatarConfig_AvatarConfig
	ids  []int32
}

var avatarConfigFunc = func() *pb.ListAvatarConfig { return &pb.ListAvatarConfig{} }

var AvatarConfig *avatarConfig

func newAvatarConfig() *avatarConfig {
	result := &avatarConfig{
		data: make(map[int32]*pb.ListAvatarConfig_AvatarConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "AvatarConfig.bytes"), avatarConfigFunc)
	if err != nil {
		logger.Errorf("load AvatarConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *avatarConfig) Item(id int32) *pb.ListAvatarConfig_AvatarConfig {
	return c.data[id]
}

func (c *avatarConfig) Items() []*pb.ListAvatarConfig_AvatarConfig {
	items := make([]*pb.ListAvatarConfig_AvatarConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *avatarConfig) GetIds() []int32 {
	return c.ids
}
