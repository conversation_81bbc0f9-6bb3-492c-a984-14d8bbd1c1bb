package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// TileMapConfig TileMapConfig

type tileMapConfig struct {
	data map[int32]*pb.ListTileMapConfig_TileMapConfig
	ids  []int32
}

var tileMapConfigFunc = func() *pb.ListTileMapConfig { return &pb.ListTileMapConfig{} }

var TileMapConfig *tileMapConfig

func newTileMapConfig() *tileMapConfig {
	result := &tileMapConfig{
		data: make(map[int32]*pb.ListTileMapConfig_TileMapConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "TileMapConfig.bytes"), tileMapConfigFunc)
	if err != nil {
		logger.Errorf("load TileMapConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *tileMapConfig) Item(id int32) *pb.ListTileMapConfig_TileMapConfig {
	return c.data[id]
}

func (c *tileMapConfig) Items() []*pb.ListTileMapConfig_TileMapConfig {
	items := make([]*pb.ListTileMapConfig_TileMapConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *tileMapConfig) GetIds() []int32 {
	return c.ids
}
