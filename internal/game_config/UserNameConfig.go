package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y 用户名生成.xlsx UserNameConfig

type userNameConfig struct {
	data map[int32]*pb.ListUserNameConfig_UserNameConfig
	ids  []int32
}

var userNameConfigFunc = func() *pb.ListUserNameConfig { return &pb.ListUserNameConfig{} }

var UserNameConfig *userNameConfig

func newUserNameConfig() *userNameConfig {
	result := &userNameConfig{
		data: make(map[int32]*pb.ListUserNameConfig_UserNameConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "UserNameConfig.bytes"), userNameConfigFunc)
	if err != nil {
		logger.Errorf("load UserNameConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *userNameConfig) Item(id int32) *pb.ListUserNameConfig_UserNameConfig {
	return c.data[id]
}

func (c *userNameConfig) Items() []*pb.ListUserNameConfig_UserNameConfig {
	items := make([]*pb.ListUserNameConfig_UserNameConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *userNameConfig) GetIds() []int32 {
	return c.ids
}
