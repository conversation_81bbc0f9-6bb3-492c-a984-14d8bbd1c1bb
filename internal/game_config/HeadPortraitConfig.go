package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// T头像表.xlsx HeadPortraitConfig

type headPortraitConfig struct {
	data map[int32]*pb.ListHeadPortraitConfig_HeadPortraitConfig
	ids  []int32
}

var headPortraitConfigFunc = func() *pb.ListHeadPortraitConfig { return &pb.ListHeadPortraitConfig{} }

var HeadPortraitConfig *headPortraitConfig

func newHeadPortraitConfig() *headPortraitConfig {
	result := &headPortraitConfig{
		data: make(map[int32]*pb.ListHeadPortraitConfig_HeadPortraitConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "HeadPortraitConfig.bytes"), headPortraitConfigFunc)
	if err != nil {
		logger.Errorf("load HeadPortraitConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *headPortraitConfig) Item(id int32) *pb.ListHeadPortraitConfig_HeadPortraitConfig {
	return c.data[id]
}

func (c *headPortraitConfig) Items() []*pb.ListHeadPortraitConfig_HeadPortraitConfig {
	items := make([]*pb.ListHeadPortraitConfig_HeadPortraitConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *headPortraitConfig) GetIds() []int32 {
	return c.ids
}
