package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// J剧情故事.xlsx StoryPlotConditionConfig

type storyPlotConditionConfig struct {
	data map[int32]*pb.ListStoryPlotConditionConfig_StoryPlotConditionConfig
	ids  []int32
}

var storyPlotConditionConfigFunc = func() *pb.ListStoryPlotConditionConfig { return &pb.ListStoryPlotConditionConfig{} }

var StoryPlotConditionConfig *storyPlotConditionConfig

func newStoryPlotConditionConfig() *storyPlotConditionConfig {
	result := &storyPlotConditionConfig{
		data: make(map[int32]*pb.ListStoryPlotConditionConfig_StoryPlotConditionConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "StoryPlotConditionConfig.bytes"), storyPlotConditionConfigFunc)
	if err != nil {
		logger.Errorf("load StoryPlotConditionConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *storyPlotConditionConfig) Item(id int32) *pb.ListStoryPlotConditionConfig_StoryPlotConditionConfig {
	return c.data[id]
}

func (c *storyPlotConditionConfig) Items() []*pb.ListStoryPlotConditionConfig_StoryPlotConditionConfig {
	items := make([]*pb.ListStoryPlotConditionConfig_StoryPlotConditionConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *storyPlotConditionConfig) GetIds() []int32 {
	return c.ids
}
