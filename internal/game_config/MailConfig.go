package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y邮件T.xlsx MailConfig

type mailConfig struct {
	data map[int32]*pb.ListMailConfig_MailConfig
	ids  []int32
}

var mailConfigFunc = func() *pb.ListMailConfig { return &pb.ListMailConfig{} }

var MailConfig *mailConfig

func newMailConfig() *mailConfig {
	result := &mailConfig{
		data: make(map[int32]*pb.ListMailConfig_MailConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "MailConfig.bytes"), mailConfigFunc)
	if err != nil {
		logger.Errorf("load MailConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *mailConfig) Item(id int32) *pb.ListMailConfig_MailConfig {
	return c.data[id]
}

func (c *mailConfig) Items() []*pb.ListMailConfig_MailConfig {
	items := make([]*pb.ListMailConfig_MailConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *mailConfig) GetIds() []int32 {
	return c.ids
}
