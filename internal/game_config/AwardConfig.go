package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// J奖励表.xlsx AwardConfig

type awardConfig struct {
	data map[int32]*pb.ListAwardConfig_AwardConfig
	ids  []int32
}

var awardConfigFunc = func() *pb.ListAwardConfig { return &pb.ListAwardConfig{} }

var AwardConfig *awardConfig

func newAwardConfig() *awardConfig {
	result := &awardConfig{
		data: make(map[int32]*pb.ListAwardConfig_AwardConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "AwardConfig.bytes"), awardConfigFunc)
	if err != nil {
		logger.Errorf("load AwardConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *awardConfig) Item(id int32) *pb.ListAwardConfig_AwardConfig {
	return c.data[id]
}

func (c *awardConfig) Items() []*pb.ListAwardConfig_AwardConfig {
	items := make([]*pb.ListAwardConfig_AwardConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *awardConfig) GetIds() []int32 {
	return c.ids
}
