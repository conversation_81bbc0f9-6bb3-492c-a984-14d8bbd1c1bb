package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// S商城表.xlsx ShopTabConfig

type shopTabConfig struct {
	data map[int32]*pb.ListShopTabConfig_ShopTabConfig
	ids  []int32
}

var shopTabConfigFunc = func() *pb.ListShopTabConfig { return &pb.ListShopTabConfig{} }

var ShopTabConfig *shopTabConfig

func newShopTabConfig() *shopTabConfig {
	result := &shopTabConfig{
		data: make(map[int32]*pb.ListShopTabConfig_ShopTabConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "ShopTabConfig.bytes"), shopTabConfigFunc)
	if err != nil {
		logger.Errorf("load ShopTabConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *shopTabConfig) Item(id int32) *pb.ListShopTabConfig_ShopTabConfig {
	return c.data[id]
}

func (c *shopTabConfig) Items() []*pb.ListShopTabConfig_ShopTabConfig {
	items := make([]*pb.ListShopTabConfig_ShopTabConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *shopTabConfig) GetIds() []int32 {
	return c.ids
}
