package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// T条件表.xlsx ConditionConfig

type conditionConfig struct {
	data map[int32]*pb.ListConditionConfig_ConditionConfig
	ids  []int32
}

var conditionConfigFunc = func() *pb.ListConditionConfig { return &pb.ListConditionConfig{} }

var ConditionConfig *conditionConfig

func newConditionConfig() *conditionConfig {
	result := &conditionConfig{
		data: make(map[int32]*pb.ListConditionConfig_ConditionConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "ConditionConfig.bytes"), conditionConfigFunc)
	if err != nil {
		logger.Errorf("load ConditionConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *conditionConfig) Item(id int32) *pb.ListConditionConfig_ConditionConfig {
	return c.data[id]
}

func (c *conditionConfig) Items() []*pb.ListConditionConfig_ConditionConfig {
	items := make([]*pb.ListConditionConfig_ConditionConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *conditionConfig) GetIds() []int32 {
	return c.ids
}
