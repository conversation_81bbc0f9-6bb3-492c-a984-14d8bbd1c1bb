package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y引导表.xlsx GuideGroupConfig

type guideGroupConfig struct {
	data map[int32]*pb.ListGuideGroupConfig_GuideGroupConfig
	ids  []int32
}

var guideGroupConfigFunc = func() *pb.ListGuideGroupConfig { return &pb.ListGuideGroupConfig{} }

var GuideGroupConfig *guideGroupConfig

func newGuideGroupConfig() *guideGroupConfig {
	result := &guideGroupConfig{
		data: make(map[int32]*pb.ListGuideGroupConfig_GuideGroupConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "GuideGroupConfig.bytes"), guideGroupConfigFunc)
	if err != nil {
		logger.Errorf("load GuideGroupConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *guideGroupConfig) Item(id int32) *pb.ListGuideGroupConfig_GuideGroupConfig {
	return c.data[id]
}

func (c *guideGroupConfig) Items() []*pb.ListGuideGroupConfig_GuideGroupConfig {
	items := make([]*pb.ListGuideGroupConfig_GuideGroupConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *guideGroupConfig) GetIds() []int32 {
	return c.ids
}
