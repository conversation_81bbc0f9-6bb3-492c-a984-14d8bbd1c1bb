package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// AI行为配置表.xlsx AINodeConfig

type aINodeConfig struct {
	data map[int32]*pb.ListAINodeConfig_AINodeConfig
	ids  []int32
}

var aINodeConfigFunc = func() *pb.ListAINodeConfig { return &pb.ListAINodeConfig{} }

var AINodeConfig *aINodeConfig

func newAINodeConfig() *aINodeConfig {
	result := &aINodeConfig{
		data: make(map[int32]*pb.ListAINodeConfig_AINodeConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "AINodeConfig.bytes"), aINodeConfigFunc)
	if err != nil {
		logger.Errorf("load AINodeConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *aINodeConfig) Item(id int32) *pb.ListAINodeConfig_AINodeConfig {
	return c.data[id]
}

func (c *aINodeConfig) Items() []*pb.ListAINodeConfig_AINodeConfig {
	items := make([]*pb.ListAINodeConfig_AINodeConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *aINodeConfig) GetIds() []int32 {
	return c.ids
}
