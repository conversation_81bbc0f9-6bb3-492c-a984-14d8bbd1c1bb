package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y英雄表.xlsx HeroQualityConfig

type heroQualityConfig struct {
	data map[int32]*pb.ListHeroQualityConfig_HeroQualityConfig
	ids  []int32
}

var heroQualityConfigFunc = func() *pb.ListHeroQualityConfig { return &pb.ListHeroQualityConfig{} }

var HeroQualityConfig *heroQualityConfig

func newHeroQualityConfig() *heroQualityConfig {
	result := &heroQualityConfig{
		data: make(map[int32]*pb.ListHeroQualityConfig_HeroQualityConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "HeroQualityConfig.bytes"), heroQualityConfigFunc)
	if err != nil {
		logger.Errorf("load HeroQualityConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *heroQualityConfig) Item(id int32) *pb.ListHeroQualityConfig_HeroQualityConfig {
	return c.data[id]
}

func (c *heroQualityConfig) Items() []*pb.ListHeroQualityConfig_HeroQualityConfig {
	items := make([]*pb.ListHeroQualityConfig_HeroQualityConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *heroQualityConfig) GetIds() []int32 {
	return c.ids
}
