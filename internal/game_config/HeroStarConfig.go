package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Y英雄表.xlsx HeroStarConfig

type heroStarConfig struct {
	data map[int32]*pb.ListHeroStarConfig_HeroStarConfig
	ids  []int32
}

var heroStarConfigFunc = func() *pb.ListHeroStarConfig { return &pb.ListHeroStarConfig{} }

var HeroStarConfig *heroStarConfig

func newHeroStarConfig() *heroStarConfig {
	result := &heroStarConfig{
		data: make(map[int32]*pb.ListHeroStarConfig_HeroStarConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "HeroStarConfig.bytes"), heroStarConfigFunc)
	if err != nil {
		logger.Errorf("load HeroStarConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *heroStarConfig) Item(id int32) *pb.ListHeroStarConfig_HeroStarConfig {
	return c.data[id]
}

func (c *heroStarConfig) Items() []*pb.ListHeroStarConfig_HeroStarConfig {
	items := make([]*pb.ListHeroStarConfig_HeroStarConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *heroStarConfig) GetIds() []int32 {
	return c.ids
}
