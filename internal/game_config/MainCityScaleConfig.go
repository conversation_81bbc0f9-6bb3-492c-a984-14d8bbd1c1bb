package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// Z主城配置.xlsx MainCityScaleConfig

type mainCityScaleConfig struct {
	data map[int32]*pb.ListMainCityScaleConfig_MainCityScaleConfig
	ids  []int32
}

var mainCityScaleConfigFunc = func() *pb.ListMainCityScaleConfig { return &pb.ListMainCityScaleConfig{} }

var MainCityScaleConfig *mainCityScaleConfig

func newMainCityScaleConfig() *mainCityScaleConfig {
	result := &mainCityScaleConfig{
		data: make(map[int32]*pb.ListMainCityScaleConfig_MainCityScaleConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "MainCityScaleConfig.bytes"), mainCityScaleConfigFunc)
	if err != nil {
		logger.Errorf("load MainCityScaleConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *mainCityScaleConfig) Item(id int32) *pb.ListMainCityScaleConfig_MainCityScaleConfig {
	return c.data[id]
}

func (c *mainCityScaleConfig) Items() []*pb.ListMainCityScaleConfig_MainCityScaleConfig {
	items := make([]*pb.ListMainCityScaleConfig_MainCityScaleConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *mainCityScaleConfig) GetIds() []int32 {
	return c.ids
}
