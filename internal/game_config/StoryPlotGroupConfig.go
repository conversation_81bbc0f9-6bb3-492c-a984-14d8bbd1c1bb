package game_config
import (
	"kairo_paradise_server/internal/consts"
	"kairo_paradise_server/internal/logger"
	"kairo_paradise_server/services/pb"
	"path/filepath"
)

// auto generated by tools, do not edit it manually
// J剧情故事.xlsx StoryPlotGroupConfig

type storyPlotGroupConfig struct {
	data map[int32]*pb.ListStoryPlotGroupConfig_StoryPlotGroupConfig
	ids  []int32
}

var storyPlotGroupConfigFunc = func() *pb.ListStoryPlotGroupConfig { return &pb.ListStoryPlotGroupConfig{} }

var StoryPlotGroupConfig *storyPlotGroupConfig

func newStoryPlotGroupConfig() *storyPlotGroupConfig {
	result := &storyPlotGroupConfig{
		data: make(map[int32]*pb.ListStoryPlotGroupConfig_StoryPlotGroupConfig),
		ids:  make([]int32, 0),
	}

	listConfig, err := loadProtoFile(filepath.Join(consts.GameConfigPath, "StoryPlotGroupConfig.bytes"), storyPlotGroupConfigFunc)
	if err != nil {
		logger.Errorf("load StoryPlotGroupConfig.bytes error: %v", err)
		return result
	}
	for _, item := range listConfig.List {
		if item.Id != nil {
			id := *item.Id
			result.data[id] = item
			result.ids = append(result.ids, id)
		}
	}
	return result
}

func (c *storyPlotGroupConfig) Item(id int32) *pb.ListStoryPlotGroupConfig_StoryPlotGroupConfig {
	return c.data[id]
}

func (c *storyPlotGroupConfig) Items() []*pb.ListStoryPlotGroupConfig_StoryPlotGroupConfig {
	items := make([]*pb.ListStoryPlotGroupConfig_StoryPlotGroupConfig, 0, len(c.data))
	for _, item := range c.data {
		items = append(items, item)
	}
	return items
}

func (c *storyPlotGroupConfig) GetIds() []int32 {
	return c.ids
}
