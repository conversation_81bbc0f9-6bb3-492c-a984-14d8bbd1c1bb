package rank

import (
	"context"
	"fmt"
	"kairo_paradise_server/internal/game_config"
	"kairo_paradise_server/internal/logger"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Manager 排行榜管理器
type Manager struct {
	// 排行榜配置
	rankConfigs map[int32]*Config
	// 缓存管理器 - 每个排行榜有独立的缓存管理器
	cacheManagers map[int32]*CacheManager
	// 全局玩家数据缓存 - 所有排行榜共享，不过期，只在更新时刷新
	playerDataCache map[uint64]*PlayerDataCache
	// 定时器
	ticker *time.Ticker
	// 互斥锁
	mutex sync.RWMutex
	// 停止信号
	stopCh chan struct{}
}

var (
	// 单例实例
	instance *Manager
	once     sync.Once
)

// GetRankManager 获取排行榜管理器实例
func GetRankManager() *Manager {
	once.Do(func() {
		instance = &Manager{
			rankConfigs:     make(map[int32]*Config),
			cacheManagers:   make(map[int32]*CacheManager),
			playerDataCache: make(map[uint64]*PlayerDataCache),
			stopCh:          make(chan struct{}),
		}
	})
	return instance
}

// Initialize 初始化排行榜管理器
func (m *Manager) Initialize() error {
	// 加载排行榜配置
	err := m.loadRankConfigs()
	if err != nil {
		return err
	}

	// 启动定时更新任务
	m.startPeriodicUpdate()

	// 启动缓存清理任务 - 为每个缓存管理器启动清理任务
	m.startCacheCleanupTasks()

	logger.Info("Rank manager initialized")
	return nil
}

// loadRankConfigs 加载排行榜配置
func (m *Manager) loadRankConfigs() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 从配置中加载排行榜信息
	for _, id := range game_config.RankConfig.GetIds() {
		config := game_config.RankConfig.Item(id)
		if config == nil {
			continue
		}

		// 创建排行榜配置
		rankConfig := &Config{
			RankType:      id,
			MinScoreLimit: config.GetMinValueLimit(),
			MaxQueryLimit: config.GetMaxQueryLimit(),
			ShowRankLimit: config.GetShowRankLimit(),
			MaxRankLimit:  config.GetMaxRankLimit(),
		}

		// 如果没有设置MaxRankLimit，设置默认值
		if rankConfig.MaxRankLimit == 0 {
			rankConfig.MaxRankLimit = 300
		}

		// 如果没有设置ShowRankLimit，使用MaxRankLimit的值
		if rankConfig.ShowRankLimit == 0 {
			rankConfig.ShowRankLimit = rankConfig.MaxRankLimit
		}

		// 如果没有设置MaxQueryLimit，设置默认值
		if rankConfig.MaxQueryLimit == 0 {
			rankConfig.MaxQueryLimit = 500 // 默认查询上限500
		}

		rankConfig.HotCacheSize = rankConfig.ShowRankLimit

		if rankConfig.UpdateInterval == 0 {
			rankConfig.IsRealtime = true
			rankConfig.CacheExpireTime = 3 // 实时排行榜缓存3分钟
		} else {
			rankConfig.CacheExpireTime = rankConfig.UpdateInterval // 定时排行榜缓存5分钟
		}
		m.rankConfigs[id] = rankConfig
		// 为每个排行榜创建独立的缓存管理器
		m.cacheManagers[id] = NewCacheManagerWithConfig(rankConfig.HotCacheSize, rankConfig.CacheExpireTime)
		logger.Info("Loaded rank config",
			zap.Int32("rankType", rankConfig.RankType),
			zap.Int32("minScoreLimit", rankConfig.MinScoreLimit),
			zap.Int32("maxQueryLimit", rankConfig.MaxQueryLimit),
			zap.Int32("showRankLimit", rankConfig.ShowRankLimit),
			zap.Int32("maxRankLimit", rankConfig.MaxRankLimit),
			zap.Int64("updateInterval", rankConfig.UpdateInterval),
			zap.Bool("isRealtime", rankConfig.IsRealtime))
	}

	return nil
}

// startPeriodicUpdate 启动定时更新任务
func (m *Manager) startPeriodicUpdate() {
	m.ticker = time.NewTicker(1 * time.Minute)

	go func() {
		for {
			select {
			case <-m.ticker.C:
				m.checkAndUpdatePeriodicRanks()
			case <-m.stopCh:
				m.ticker.Stop()
				return
			}
		}
	}()
}

// startCacheCleanupTasks 启动缓存清理任务
func (m *Manager) startCacheCleanupTasks() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 为每个排行榜启动缓存清理任务
	for rankType, cacheManager := range m.cacheManagers {
		go func(rt int32, cm *CacheManager) {
			logger.Info("Starting cache cleanup task", zap.Int32("rankType", rt))
			cm.StartCacheCleanup(m.stopCh)
		}(rankType, cacheManager)
	}

	// 注意：玩家基础数据缓存不需要定期清理，只在数据更新时刷新
	logger.Info("Player data cache will not expire automatically, only updated when rank data changes")
}

// getCacheManager 获取指定排行榜的缓存管理器
func (m *Manager) getCacheManager(rankType int32) *CacheManager {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.cacheManagers[rankType]
}

// getPlayerDataCache 获取玩家基础数据缓存
func (m *Manager) getPlayerDataCache(playerID uint64) *PlayerDataCache {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	cache, exists := m.playerDataCache[playerID]
	if !exists {
		return nil
	}
	return cache
}

// setPlayerDataCache 设置玩家基础数据缓存
func (m *Manager) setPlayerDataCache(playerID uint64, playerName string, level int32) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查是否已存在缓存，如果数据没有变化则不更新
	if existingCache, exists := m.playerDataCache[playerID]; exists {
		if existingCache.PlayerName == playerName && existingCache.Level == level {
			// 数据没有变化，不需要更新
			return
		}
	}

	cache := &PlayerDataCache{
		PlayerID:   playerID,
		PlayerName: playerName,
		Level:      level,
		UpdateTime: time.Now().Unix(),
	}

	m.playerDataCache[playerID] = cache
	logger.Debug("Updated player data cache",
		zap.Uint64("playerID", playerID),
		zap.String("playerName", playerName),
		zap.Int32("level", level))
}

// clearPlayerDataCache 清理指定玩家的数据缓存（用于玩家数据重大变更时）
func (m *Manager) clearPlayerDataCache(playerID uint64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.playerDataCache[playerID]; exists {
		delete(m.playerDataCache, playerID)
		logger.Debug("Cleared player data cache", zap.Uint64("playerID", playerID))
	}
}

// clearAllPlayerDataCache 清理所有玩家数据缓存（用于系统维护）
func (m *Manager) clearAllPlayerDataCache() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	count := len(m.playerDataCache)
	m.playerDataCache = make(map[uint64]*PlayerDataCache)
	logger.Info("Cleared all player data cache", zap.Int("count", count))
}

// buildRankInfo 构建完整的排名信息（组合玩家基础数据和排名数据）
func (m *Manager) buildRankInfo(playerID uint64, rankCache *PlayerRankCache) *RankInfo {
	if rankCache == nil {
		return &RankInfo{Rank: -1}
	}

	if rankCache.Rank == -1 {
		return &RankInfo{Rank: -1}
	}

	// 获取玩家基础数据
	playerData := m.getPlayerDataCache(playerID)

	entry := Entry{
		PlayerID:   playerID,
		Prosperity: rankCache.Score,
	}

	// 如果有缓存的玩家基础数据，使用缓存数据
	if playerData != nil {
		entry.PlayerName = playerData.PlayerName
		entry.Level = playerData.Level
	} else {
		// 如果没有缓存，从Redis获取（这种情况应该很少发生）
		if fullEntry, err := getRankEntry(context.Background(), 0, playerID); err == nil && fullEntry != nil {
			entry.PlayerName = fullEntry.PlayerName
			entry.Level = fullEntry.Level
			// 同时缓存玩家基础数据
			m.setPlayerDataCache(playerID, fullEntry.PlayerName, fullEntry.Level)
		}
	}

	return &RankInfo{
		Rank:  rankCache.Rank,
		Entry: entry,
	}
}

// Stop 停止排行榜管理器
func (m *Manager) Stop() {
	close(m.stopCh)
}

// checkAndUpdatePeriodicRanks 检查并更新定时排行榜
func (m *Manager) checkAndUpdatePeriodicRanks() {
	ctx := context.Background()
	now := time.Now().Unix()

	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for rankType, _ := range m.rankConfigs {
		config := m.rankConfigs[rankType]
		if config == nil || config.IsRealtime {
			continue
		}
		// 获取上次更新时间
		lastUpdate, err := getLastUpdateTime(ctx, rankType)
		if err != nil {
			logger.Error("Failed to get last update time", zap.Error(err), zap.Int32("rankType", rankType))
			continue
		}

		// 如果达到更新时间，则更新排行榜
		if now-lastUpdate >= config.UpdateInterval {
			logger.Info("Updating periodic rank", zap.Int32("rankType", rankType))

			// 更新排行榜
			// TODO 这里实际上应该从游戏服或数据库获取最新数据
			// 目前只更新时间戳
			err := updateLastUpdateTime(ctx, rankType)
			if err != nil {
				logger.Error("Failed to update last update time", zap.Error(err), zap.Int32("rankType", rankType))
			}
		}
	}
}

// UpdateRankEntry 更新排行榜条目
func (m *Manager) UpdateRankEntry(ctx context.Context, rankType int32, entry *Entry) (int32, error) {
	config, exists := m.rankConfigs[rankType]
	if !exists {
		return -1, fmt.Errorf("rank type %d not found", rankType)
	}
	// 检查分数是否达到上榜要求
	if entry.Prosperity < config.MinScoreLimit {
		return -1, nil
	}

	// 设置合格缓存
	_ = setRankEligibility(ctx, rankType, entry.PlayerID, true)
	// 保存排行榜到redis中
	minScore, err := saveRankEntry(ctx, rankType, config.MaxRankLimit, entry, config.IsRealtime)
	if err != nil {
		return -1, err
	}
	// 更新最低上榜分数
	m.SetMinScoreLimit(rankType, minScore)
	// 获取玩家排名
	rank, err := getPlayerRank(ctx, rankType, entry.PlayerID, config.IsRealtime)
	if err != nil {
		return -1, err
	}
	// 如果排名超出最大排行榜数量，则返回-1
	if rank > config.MaxRankLimit {
		return -1, nil
	}
	// 保存玩家的基础数据到全局缓存
	m.setPlayerDataCache(entry.PlayerID, entry.PlayerName, entry.Level)

	// 保存玩家的排名数据到对应排行榜缓存
	cacheManager := m.getCacheManager(rankType)
	if cacheManager != nil {
		cacheManager.SetPlayerRankCache(entry.PlayerID, rank, entry.Prosperity)
	}

	return rank, nil
}

// GetRankList 获取排行榜列表
func (m *Manager) GetRankList(ctx context.Context, rankType int32, start, count int32) ([]*Entry, int64, error) {
	// 读的时候不加锁，加锁影响性能，而且也没有必要加锁，没有竞态条件
	config, exists := m.rankConfigs[rankType]
	if !exists {
		return nil, 0, fmt.Errorf("rank type %d not found", rankType)
	}

	// 检查请求参数，只获取排行榜的显示部分数据
	if count > config.ShowRankLimit {
		count = config.ShowRankLimit
	}

	// 获取对应的缓存管理器
	cacheManager := m.getCacheManager(rankType)

	// 尝试从缓存获取
	var cache *Cache
	if cacheManager != nil {
		cache = cacheManager.GetHotRankCache(rankType)
	}

	if cache != nil && start == 1 && count <= int32(len(cache.Entries)) {
		// 缓存命中，直接返回缓存数据
		endIndex := count
		if endIndex > int32(len(cache.Entries)) {
			endIndex = int32(len(cache.Entries))
		}
		return cache.Entries[0:endIndex], cache.TotalCount, nil
	}

	// 缓存未命中，使用优化的Redis获取方法
	entries, totalCount, err := GetRankRangeOptimized(ctx, rankType, start, count, config.IsRealtime)
	if err != nil {
		return nil, 0, err
	}

	// 如果是获取前面的数据，更新热点缓存
	if start == 1 && len(entries) > 0 && cacheManager != nil {
		cacheManager.SetHotRankCache(rankType, entries, totalCount)
	}

	return entries, totalCount, nil
}

// GetPlayerRank 获取玩家排名
func (m *Manager) GetPlayerRank(ctx context.Context, rankType int32, playerID uint64) (*RankInfo, error) {
	config, exists := m.rankConfigs[rankType]
	if !exists {
		return nil, fmt.Errorf("rank type %d not found", rankType)
	}

	// 获取对应的缓存管理器
	cacheManager := m.getCacheManager(rankType)

	// 尝试从缓存获取排名信息
	var rankCache *PlayerRankCache
	if cacheManager != nil {
		rankCache = cacheManager.GetPlayerRankCache(playerID)
	}

	if rankCache != nil {
		return m.buildRankInfo(playerID, rankCache), nil
	}

	// 检查玩家是否有资格上榜
	eligible, err := getRankEligibility(ctx, rankType, playerID)
	if err != nil {
		logger.Warn("Failed to get rank eligibility", zap.Error(err))
		// 继续执行，不要因为缓存错误影响查询
	} else if !eligible {
		// 如果缓存显示玩家不合格，直接返回并缓存结果
		if cacheManager != nil {
			cacheManager.SetPlayerRankCache(playerID, -1, 0)
		}
		return &RankInfo{Rank: -1}, nil
	}

	// 获取玩家排名
	rank, err := getPlayerRank(ctx, rankType, playerID, config.IsRealtime)
	if err != nil {
		return nil, err
	}
	// 如果玩家未上榜
	if rank == -1 {
		if cacheManager != nil {
			cacheManager.SetPlayerRankCache(playerID, -1, 0)
		}
		return &RankInfo{Rank: -1}, nil
	}

	// 获取玩家排行榜条目
	entry, err := getRankEntry(ctx, rankType, playerID)
	if err != nil {
		return nil, err
	}
	if entry == nil {
		if cacheManager != nil {
			cacheManager.SetPlayerRankCache(playerID, -1, 0)
		}
		return &RankInfo{Rank: -1}, nil
	}

	// 缓存玩家基础数据
	m.setPlayerDataCache(entry.PlayerID, entry.PlayerName, entry.Level)

	// 缓存排名结果
	if cacheManager != nil {
		cacheManager.SetPlayerRankCache(playerID, rank, entry.Prosperity)
	}

	return &RankInfo{
		Rank:  rank,
		Entry: *entry,
	}, nil
}

// GetMultiPlayerRank 批量获取玩家排名
func (m *Manager) GetMultiPlayerRank(ctx context.Context, rankType int32, playerIDs []uint64) ([]*RankInfo, error) {
	m.mutex.RLock()
	config, exists := m.rankConfigs[rankType]
	m.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("rank type %d not found", rankType)
	}

	// 获取对应的缓存管理器
	cacheManager := m.getCacheManager(rankType)
	if cacheManager == nil {
		// 如果没有缓存管理器，直接从Redis获取
		return BatchGetPlayerRanks(ctx, rankType, playerIDs, config.IsRealtime)
	}

	// 使用缓存管理器的批量获取方法
	results, err := cacheManager.BatchGetPlayerRanks(ctx, playerIDs,
		func(ctx context.Context, uncachedIDs []uint64) ([]*RankInfo, error) {
			// 批量获取未缓存的玩家排名
			return BatchGetPlayerRanks(ctx, rankType, uncachedIDs, config.IsRealtime)
		})

	if err != nil {
		return nil, err
	}

	// 为每个结果补充完整的玩家基础数据
	for i, result := range results {
		if result != nil && result.Rank != -1 {
			playerID := playerIDs[i]
			// 缓存玩家基础数据
			m.setPlayerDataCache(result.Entry.PlayerID, result.Entry.PlayerName, result.Entry.Level)
			// 重新构建完整的RankInfo
			rankCache := cacheManager.GetPlayerRankCache(playerID)
			if rankCache != nil {
				results[i] = m.buildRankInfo(playerID, rankCache)
			}
		}
	}

	return results, nil
}

// ClearRank 清除排行榜
func (m *Manager) ClearRank(ctx context.Context, rankType int32) error {
	m.mutex.RLock()
	config, exists := m.rankConfigs[rankType]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("rank type %d not found", rankType)
	}

	// 清除Redis中的排行榜数据
	err := clearRank(ctx, rankType, config.IsRealtime)
	if err != nil {
		return err
	}

	// 清除本地缓存
	cacheManager := m.getCacheManager(rankType)
	if cacheManager != nil {
		cacheManager.ClearRankCache(rankType)
	}

	return nil
}

// GetCacheStats 获取缓存统计信息
func (m *Manager) GetCacheStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]interface{})

	// 添加每个排行榜的缓存统计
	for rankType, cacheManager := range m.cacheManagers {
		if cacheManager != nil {
			stats[fmt.Sprintf("rank_%d", rankType)] = cacheManager.GetCacheStats()
		}
	}

	// 添加全局玩家数据缓存统计
	stats["global_player_data_cache"] = map[string]interface{}{
		"player_data_cache_count": len(m.playerDataCache),
		"cache_policy":            "no_expiration_update_on_rank_change",
	}

	return stats
}

// SetCacheExpireTime 设置指定排行榜的缓存过期时间
func (m *Manager) SetCacheExpireTime(rankType int32, expireTime int64) {
	cacheManager := m.getCacheManager(rankType)
	if cacheManager != nil {
		cacheManager.cacheExpireTime = expireTime
	}
}

// SetHotCacheSize 设置指定排行榜的热点缓存大小
func (m *Manager) SetHotCacheSize(rankType int32, size int32) {
	cacheManager := m.getCacheManager(rankType)
	if cacheManager != nil {
		cacheManager.hotCacheSize = size
	}
}

// SetAllCacheExpireTime 设置所有排行榜的缓存过期时间
func (m *Manager) SetAllCacheExpireTime(expireTime int64) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for _, cacheManager := range m.cacheManagers {
		if cacheManager != nil {
			cacheManager.cacheExpireTime = expireTime
		}
	}
}

// SetAllHotCacheSize 设置所有排行榜的热点缓存大小
func (m *Manager) SetAllHotCacheSize(size int32) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for _, cacheManager := range m.cacheManagers {
		if cacheManager != nil {
			cacheManager.hotCacheSize = size
		}
	}
}

// ClearPlayerDataCache 清理指定玩家的数据缓存（公开方法）
func (m *Manager) ClearPlayerDataCache(playerID uint64) {
	m.clearPlayerDataCache(playerID)
}

// ClearAllPlayerDataCache 清理所有玩家数据缓存（公开方法）
func (m *Manager) ClearAllPlayerDataCache() {
	m.clearAllPlayerDataCache()
}

// SetMinScoreLimit 设置最低分数限制，每次有上榜更新，都需要更新这个值
func (m *Manager) SetMinScoreLimit(rankType int32, minScoreLimit int32) {
	// 这里需要加锁，避免竞态条件导致数据不一致
	m.mutex.Lock()
	defer m.mutex.Unlock()

	config, exists := m.rankConfigs[rankType]
	if !exists {
		return
	}
	if config.MinScoreLimit != minScoreLimit {
		config.MinScoreLimit = minScoreLimit
		// TODO 下发至游戏服，用于游戏服拦截更新榜单请求
	}
}
