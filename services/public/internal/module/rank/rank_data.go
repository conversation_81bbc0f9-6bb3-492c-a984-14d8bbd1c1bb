package rank

import (
	"kairo_paradise_server/services/pb"
	"time"
)

// Config represents the configuration for a leaderboard
type Config struct {
	RankType       int32 // 排行榜类型，对应 ERankType
	MinScoreLimit  int32 // 最低分数限制，低于此分数不能上榜
	MaxQueryLimit  int32 // 查询人数上限
	ShowRankLimit  int32 // 榜上显示数量
	MaxRankLimit   int32 // 排行数量
	UpdateInterval int32 // 更新间隔（秒），0表示实时更新
	IsRealtime     bool  // 是否为实时排行榜

	// 队列配置
	QueueSize   int32 // 队列大小
	WorkerCount int32 // 工作器数量
	BatchSize   int32 // 批处理大小
}

// Entry represents an entry in the leaderboard
type Entry struct {
	PlayerID   uint64 // 玩家ID
	PlayerName string // 玩家名称
	Level      int32  // 玩家等级
	Prosperity int32  // 繁荣度
	UpdateTime int64  // 更新时间
	Ranking    int32  // 排名
}

// ToProto converts a RankEntry to a protobuf RankEntryData
func (e *Entry) ToProto() *pb.RankEntryData {
	return &pb.RankEntryData{
		PlayerId:   &e.PlayerID,
		PlayerName: &e.PlayerName,
		Level:      &e.Level,
		Prosperity: &e.Prosperity,
	}
}

// FromProto converts a protobuf RankEntryData to a RankEntry
func (e *Entry) FromProto(data *pb.RankEntryData) {
	if data.PlayerId != nil {
		e.PlayerID = *data.PlayerId
	}
	if data.PlayerName != nil {
		e.PlayerName = *data.PlayerName
	}
	if data.Level != nil {
		e.Level = *data.Level
	}
	if data.Prosperity != nil {
		e.Prosperity = *data.Prosperity
	}
	e.UpdateTime = time.Now().Unix()
}

// NewRankEntryFromProto creates a new RankEntry from a protobuf RankEntryData
func NewRankEntryFromProto(data *pb.RankEntryData) *Entry {
	entry := &Entry{}
	entry.FromProto(data)
	return entry
}

// UpdateRequest represents a rank update request
type UpdateRequest struct {
	RankType int32  // 排行榜类型
	Entry    *Entry // 排行榜条目
	Priority int32  // 优先级，数值越小优先级越高
}

// UpdateResult represents the result of a rank update
type UpdateResult struct {
	PlayerID uint64 // 玩家ID
	RankType int32  // 排行榜类型
	NewRank  int32  // 新排名，-1表示未上榜
	OldRank  int32  // 旧排名，-1表示之前未上榜
	Success  bool   // 是否成功
	Error    error  // 错误信息
}

// QueueStats represents queue statistics
type QueueStats struct {
	QueueSize      int32 // 当前队列大小
	ProcessedCount int64 // 已处理数量
	ErrorCount     int64 // 错误数量
	WorkerCount    int32 // 工作器数量
}

// MemoryRankData represents in-memory rank data
type MemoryRankData struct {
	Entries    []*Entry          // 排行榜条目列表（按分数排序）
	PlayerMap  map[uint64]*Entry // 玩家ID到条目的映射
	TotalCount int64             // 总条目数
	UpdateTime int64             // 最后更新时间
}

// NewMemoryRankData creates a new MemoryRankData
func NewMemoryRankData() *MemoryRankData {
	return &MemoryRankData{
		Entries:    make([]*Entry, 0),
		PlayerMap:  make(map[uint64]*Entry),
		TotalCount: 0,
		UpdateTime: time.Now().Unix(),
	}
}
