package rank

import (
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	"sync"
	"sync/atomic"
	"time"
)

// UpdateQueue 排行榜更新队列
type UpdateQueue struct {
	// 队列通道
	queue chan *UpdateRequest
	// 结果通道
	resultChan chan *UpdateResult
	// 工作器数量
	workerCount int32
	// 批处理大小
	batchSize int32
	// 统计信息
	processedCount int64
	errorCount     int64
	// 停止信号
	stopCh chan struct{}
	// 等待组
	wg sync.WaitGroup
	// 内存存储
	memoryStore *MemoryStore
	// 配置
	config *Config
	// 排行榜类型
	rankType int32
}

// NewUpdateQueue 创建新的更新队列
func NewUpdateQueue(rankType int32, config *Config, memoryStore *MemoryStore) *UpdateQueue {
	queueSize := config.QueueSize
	if queueSize <= 0 {
		queueSize = 1000 // 默认队列大小
	}

	workerCount := config.WorkerCount
	if workerCount <= 0 {
		workerCount = 2 // 默认工作器数量
	}

	batchSize := config.BatchSize
	if batchSize <= 0 {
		batchSize = 10 // 默认批处理大小
	}

	return &UpdateQueue{
		queue:       make(chan *UpdateRequest, queueSize),
		resultChan:  make(chan *UpdateResult, queueSize),
		workerCount: workerCount,
		batchSize:   batchSize,
		stopCh:      make(chan struct{}),
		memoryStore: memoryStore,
		config:      config,
		rankType:    rankType,
	}
}

// Start 启动队列处理
func (uq *UpdateQueue) Start() {
	logger.Info("Starting update queue",
		zap.Int32("rankType", uq.rankType),
		zap.Int32("workerCount", uq.workerCount),
		zap.Int32("queueSize", int32(cap(uq.queue))))

	// 启动工作器
	for i := int32(0); i < uq.workerCount; i++ {
		uq.wg.Add(1)
		go uq.worker(i)
	}

	// 启动结果处理器
	uq.wg.Add(1)
	go uq.resultProcessor()
}

// Stop 停止队列处理
func (uq *UpdateQueue) Stop() {
	logger.Info("Stopping update queue", zap.Int32("rankType", uq.rankType))
	close(uq.stopCh)
	uq.wg.Wait()
	close(uq.queue)
	close(uq.resultChan)
}

// AddUpdateRequest 添加更新请求
func (uq *UpdateQueue) AddUpdateRequest(request *UpdateRequest) bool {
	select {
	case uq.queue <- request:
		return true
	default:
		// 队列已满
		atomic.AddInt64(&uq.errorCount, 1)
		logger.Warn("Update queue is full",
			zap.Int32("rankType", uq.rankType),
			zap.Uint64("playerID", request.Entry.PlayerID))
		return false
	}
}

// worker 工作器
func (uq *UpdateQueue) worker(workerID int32) {
	defer uq.wg.Done()

	logger.Info("Update queue worker started",
		zap.Int32("rankType", uq.rankType),
		zap.Int32("workerID", workerID))

	batch := make([]*UpdateRequest, 0, uq.batchSize)
	ticker := time.NewTicker(100 * time.Millisecond) // 100ms批处理间隔
	defer ticker.Stop()

	for {
		select {
		case <-uq.stopCh:
			// 处理剩余的批次
			if len(batch) > 0 {
				uq.processBatch(batch, workerID)
			}
			logger.Info("Update queue worker stopped",
				zap.Int32("rankType", uq.rankType),
				zap.Int32("workerID", workerID))
			return

		case request := <-uq.queue:
			batch = append(batch, request)

			// 如果批次已满，立即处理
			if len(batch) >= int(uq.batchSize) {
				uq.processBatch(batch, workerID)
				batch = batch[:0] // 重置批次
			}

		case <-ticker.C:
			// 定时处理批次
			if len(batch) > 0 {
				uq.processBatch(batch, workerID)
				batch = batch[:0] // 重置批次
			}
		}
	}
}

// processBatch 处理批次
func (uq *UpdateQueue) processBatch(batch []*UpdateRequest, workerID int32) {
	if len(batch) == 0 {
		return
	}

	logger.Debug("Processing batch",
		zap.Int32("rankType", uq.rankType),
		zap.Int32("workerID", workerID),
		zap.Int("batchSize", len(batch)))

	for _, request := range batch {
		result := uq.processUpdateRequest(request)

		// 发送结果
		select {
		case uq.resultChan <- result:
		default:
			// 结果通道已满，记录错误
			logger.Warn("Result channel is full",
				zap.Int32("rankType", uq.rankType),
				zap.Uint64("playerID", request.Entry.PlayerID))
		}

		atomic.AddInt64(&uq.processedCount, 1)
	}
}

// processUpdateRequest 处理单个更新请求
func (uq *UpdateQueue) processUpdateRequest(request *UpdateRequest) *UpdateResult {
	result := &UpdateResult{
		PlayerID: request.Entry.PlayerID,
		RankType: request.RankType,
		NewRank:  -1,
		OldRank:  -1,
		Success:  false,
	}

	// 更新内存存储
	newRank, oldRank, err := uq.memoryStore.UpdateEntry(request.RankType, request.Entry, uq.config)
	if err != nil {
		result.Error = err
		atomic.AddInt64(&uq.errorCount, 1)
		logger.Error("Failed to update rank entry",
			zap.Error(err),
			zap.Int32("rankType", request.RankType),
			zap.Uint64("playerID", request.Entry.PlayerID))
		return result
	}

	result.NewRank = newRank
	result.OldRank = oldRank
	result.Success = true

	return result
}

// resultProcessor 结果处理器
func (uq *UpdateQueue) resultProcessor() {
	defer uq.wg.Done()

	logger.Info("Result processor started", zap.Int32("rankType", uq.rankType))

	for {
		select {
		case <-uq.stopCh:
			logger.Info("Result processor stopped", zap.Int32("rankType", uq.rankType))
			return

		case result := <-uq.resultChan:
			uq.handleResult(result)
		}
	}
}

// handleResult 处理结果
func (uq *UpdateQueue) handleResult(result *UpdateResult) {
	if !result.Success {
		logger.Error("Rank update failed",
			zap.Error(result.Error),
			zap.Int32("rankType", result.RankType),
			zap.Uint64("playerID", result.PlayerID))
		return
	}

	// 记录排名变化
	if result.OldRank != result.NewRank {
		logger.Debug("Rank changed",
			zap.Int32("rankType", result.RankType),
			zap.Uint64("playerID", result.PlayerID),
			zap.Int32("oldRank", result.OldRank),
			zap.Int32("newRank", result.NewRank))

		// 这里可以添加排名变化的通知逻辑
		// 例如：通知游戏服务器、发送给客户端等
	}
}

// GetStats 获取队列统计信息
func (uq *UpdateQueue) GetStats() QueueStats {
	return QueueStats{
		QueueSize:      int32(len(uq.queue)),
		ProcessedCount: atomic.LoadInt64(&uq.processedCount),
		ErrorCount:     atomic.LoadInt64(&uq.errorCount),
		WorkerCount:    uq.workerCount,
	}
}

// GetQueueLength 获取当前队列长度
func (uq *UpdateQueue) GetQueueLength() int32 {
	return int32(len(uq.queue))
}

// IsQueueFull 检查队列是否已满
func (uq *UpdateQueue) IsQueueFull() bool {
	return len(uq.queue) >= cap(uq.queue)
}
